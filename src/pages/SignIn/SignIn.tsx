import React from 'react';
import { SignIn as ClerkSignIn } from '@clerk/clerk-react';
import { useSearchParams } from 'react-router-dom';
import { AuthHeader, MinimalHero, SocialProof } from '../../components/Auth';
import { clerkAppearance } from '../../styles/clerkAppearance';

const SignIn: React.FC = () => {
  const [searchParams] = useSearchParams();
  const redirectUrl = searchParams.get('redirect') || '/';

  return (
    <div className="min-h-screen bg-white">
      {/* Mobile-First Header */}
      <AuthHeader
        actionLink={{
          to: "/sign-up",
          text: "Registrer deg",
          ariaLabel: "Gå til registreringssiden"
        }}
      />

      {/* Main Content Container */}
      <main className="w-full px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-md mx-auto space-y-8">

          {/* Minimal Hero Section */}
          <MinimalHero
            title="Logg inn på JobbLogg"
            subtitle="Fortsett med prosjektdokumentasjonen din"
            className="py-6"
          />

          {/* Sign-in Form - Hovedfokus */}
          <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8">
            <ClerkSignIn
              fallbackRedirectUrl={redirectUrl}
              signUpUrl="/sign-up"
              appearance={{
                ...clerkAppearance,
                elements: {
                  ...clerkAppearance.elements,
                  rootBox: "w-full",
                  card: "shadow-none border-none bg-transparent p-0",
                  headerTitle: "text-xl font-semibold text-jobblogg-text-strong mb-2",
                  headerSubtitle: "text-sm text-jobblogg-text-medium mb-6",
                  formButtonPrimary: "bg-jobblogg-primary hover:bg-jobblogg-primary-dark text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 min-h-[48px] text-base focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20 focus:ring-offset-2",
                  formFieldInput: "border-jobblogg-border focus:border-jobblogg-primary focus:ring-jobblogg-primary/20 rounded-lg px-4 py-3 text-base min-h-[48px] transition-all duration-200",
                  formFieldLabel: "text-sm font-medium text-jobblogg-text-strong mb-2",
                  socialButtonsBlockButton: "border-jobblogg-border hover:bg-jobblogg-neutral/10 rounded-lg py-3 px-4 text-sm font-medium transition-all duration-200 min-h-[48px]",
                  dividerLine: "bg-jobblogg-border",
                  dividerText: "text-jobblogg-text-muted text-sm",
                  footerActionLink: "text-jobblogg-primary hover:text-jobblogg-primary-dark font-medium transition-colors",
                  identityPreviewText: "text-jobblogg-text-medium text-sm",
                  identityPreviewEditButton: "text-jobblogg-primary hover:text-jobblogg-primary-dark text-sm font-medium",
                }
              }}
            />
          </div>

          {/* Social Proof */}
          <SocialProof
            count="100+"
            description="håndverksbedrifter"
          />

        </div>
      </main>
    </div>
  );
};
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:flex lg:min-h-screen">
        {/* Left Side - Brand & Context Section */}
        <div className="lg:w-1/2 relative bg-gradient-to-br from-jobblogg-primary via-jobblogg-primary-dark to-jobblogg-primary-light overflow-hidden">
          {/* Background Pattern/Texture */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }} />
          </div>

          {/* Content Container */}
          <div className="relative z-10 h-full flex flex-col justify-between p-8 lg:p-12 min-h-screen">
            {/* Logo and Header */}
            <div className="space-y-8">
              {/* Logo */}
              <div className="animate-fade-in">
                <Link
                  to="/"
                  className="inline-flex bg-white/95 backdrop-blur-sm rounded-xl px-4 py-3 shadow-soft border border-white/20 hover:bg-white hover:shadow-medium transition-all duration-200 cursor-pointer"
                  aria-label="Gå til forsiden"
                >
                  <JobbLoggLogo size="lg" />
                </Link>
              </div>

              {/* Value Proposition */}
              <div className="space-y-4 animate-slide-up" style={{ animationDelay: '200ms' }}>
                <h1 className="text-3xl lg:text-4xl xl:text-5xl font-bold text-white leading-tight">
                  Dokumenter og kommuniser<br />
                  undervegs.
                </h1>
                <p className="text-lg lg:text-xl text-white/90 max-w-md leading-relaxed">
                  Hold kunden oppdatert mens du jobber – ikke bare når du er ferdig.
                </p>
              </div>
            </div>

            {/* Use Case Example */}
            <div className="animate-slide-up" style={{ animationDelay: '400ms' }}>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-white font-semibold text-base lg:text-lg mb-3">Kjøkkenrenovering - dag 3</h3>
                    <div className="space-y-2 text-sm lg:text-base">
                      <div className="flex items-start space-x-2">
                        <span className="text-white/70 font-medium">09:30</span>
                        <span className="text-white/95">"Starter med rørleggerarbeid i dag 🔧"</span>
                      </div>
                      <div className="flex items-start space-x-2">
                        <span className="text-white/70 font-medium">14:15</span>
                        <span className="text-white/95">"Fant gammel vannlekkasje - sender bilder"</span>
                      </div>
                      <div className="flex items-start space-x-2">
                        <span className="text-white/70 font-medium">16:00</span>
                        <span className="text-white/95">"Alt fikset! Klar for flislegging i morgen ✅"</span>
                      </div>
                    </div>
                    <div className="text-white/70 text-xs mt-3 italic">
                      Kunden ser alt i sanntid og kan stille spørsmål undervegs
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Sign-in Section */}
        <div className="lg:w-1/2 flex flex-col items-center justify-center min-h-screen p-6 sm:p-8 lg:p-12 xl:p-16">
          <div className="w-full max-w-md space-y-8">
            {/* Clerk SignIn Component */}
            <div className="animate-scale-in" style={{ animationDelay: '300ms' }}>
              <ClerkSignIn
                fallbackRedirectUrl={redirectUrl}
                signUpUrl="/sign-up"
                appearance={{
                  variables: {
                    fontFamily: 'Inter, system-ui, sans-serif',
                    fontSize: '16px',
                    borderRadius: '0.75rem',
                    colorPrimary: '#2563EB',
                    colorText: '#111827',
                    colorTextSecondary: '#4B5563',
                    colorBackground: '#ffffff',
                    colorInputBackground: '#ffffff',
                    colorInputText: '#111827',
                    spacingUnit: '1rem',
                  },
                  elements: {
                    rootBox: {
                      width: '100%',
                      fontFamily: 'Inter, system-ui, sans-serif',
                    },
                    card: {
                      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                      border: 'none',
                      backgroundColor: 'transparent',
                      padding: '2.5rem',
                      width: '100%',
                      borderRadius: '1rem',
                    },
                    headerTitle: {
                      fontSize: '2rem',
                      fontWeight: '700',
                      color: '#1F2937',
                      marginBottom: '0.75rem',
                      fontFamily: 'Inter, system-ui, sans-serif',
                      lineHeight: '1.25',
                      letterSpacing: '-0.025em',
                    },
                    headerSubtitle: {
                      color: '#6B7280',
                      fontSize: '0.875rem',
                      fontWeight: '400',
                      marginBottom: '2.5rem',
                      fontFamily: 'Inter, system-ui, sans-serif',
                      lineHeight: '1.5',
                    },
                    formButtonPrimary: {
                      background: 'linear-gradient(135deg, #2563EB 0%, #1E40AF 100%)',
                      color: '#ffffff',
                      fontWeight: '600',
                      fontSize: '1rem',
                      fontFamily: 'Inter, system-ui, sans-serif',
                      padding: '0.875rem 1.5rem',
                      height: '3rem',
                      width: '100%',
                      borderRadius: '0.75rem',
                      border: 'none',
                      cursor: 'pointer',
                      boxShadow: '0 4px 14px 0 rgba(37, 99, 235, 0.25)',
                      transition: 'all 0.2s ease-out',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 6px 20px 0 rgba(37, 99, 235, 0.35)',
                      },
                      '&:active': {
                        transform: 'translateY(0px)',
                      },
                      '&:focus': {
                        outline: 'none',
                        boxShadow: '0 0 0 2px rgba(37, 99, 235, 0.2), 0 0 0 4px rgba(37, 99, 235, 0.1)',
                      },
                      '&:disabled': {
                        opacity: '0.5',
                        cursor: 'not-allowed',
                        transform: 'none',
                      },
                    },
                    formFieldInput: {
                      border: '1px solid #D1D5DB',
                      backgroundColor: '#ffffff',
                      color: '#111827',
                      borderRadius: '0.75rem',
                      padding: '0.75rem 1rem',
                      height: '3rem',
                      width: '100%',
                      fontSize: '1rem',
                      fontFamily: 'Inter, system-ui, sans-serif',
                      transition: 'all 0.2s ease-out',
                      '&::placeholder': {
                        color: '#6B7280',
                      },
                      '&:hover': {
                        borderColor: '#9CA3AF',
                      },
                      '&:focus': {
                        outline: 'none',
                        borderColor: '#2563EB',
                        boxShadow: '0 0 0 3px rgba(37, 99, 235, 0.1)',
                      },
                    },
                    formFieldLabel: {
                      color: '#374151',
                      fontWeight: '500',
                      fontSize: '0.875rem',
                      marginBottom: '0.75rem',
                      fontFamily: 'Inter, system-ui, sans-serif',
                    },
                    footerActionLink: {
                      color: '#2563EB',
                      fontWeight: '600',
                      fontSize: '0.875rem',
                      fontFamily: 'Inter, system-ui, sans-serif',
                      textDecoration: 'none',
                      transition: 'all 0.2s ease-out',
                      '&:hover': {
                        color: '#1E40AF',
                        textDecoration: 'underline',
                        textUnderlineOffset: '2px',
                      },
                      '&:focus': {
                        outline: 'none',
                        boxShadow: '0 0 0 2px rgba(37, 99, 235, 0.2)',
                        borderRadius: '0.25rem',
                      },
                    },
                    dividerLine: {
                      backgroundColor: '#E5E7EB',
                      height: '1px',
                    },
                    dividerText: {
                      color: '#6B7280',
                      fontSize: '0.875rem',
                      fontFamily: 'Inter, system-ui, sans-serif',
                      fontWeight: '500',
                    },
                    socialButtonsBlockButton: {
                      border: '1px solid #E5E7EB',
                      backgroundColor: '#ffffff',
                      color: '#111827',
                      borderRadius: '0.75rem',
                      padding: '0.75rem 1rem',
                      height: '3rem',
                      width: '100%',
                      fontWeight: '500',
                      fontSize: '0.875rem',
                      fontFamily: 'Inter, system-ui, sans-serif',
                      transition: 'all 0.2s ease-out',
                      boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
                      cursor: 'pointer',
                      '&:hover': {
                        backgroundColor: '#FAFAF9',
                        borderColor: '#D1D5DB',
                        boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
                      },
                      '&:focus': {
                        outline: 'none',
                        boxShadow: '0 0 0 2px rgba(37, 99, 235, 0.2), 0 0 0 4px rgba(37, 99, 235, 0.1)',
                      },
                    },
                    formFieldErrorText: {
                      color: '#DC2626',
                      fontSize: '0.875rem',
                      marginTop: '0.25rem',
                      fontFamily: 'Inter, system-ui, sans-serif',
                      fontWeight: '500',
                    },
                    formFieldSuccessText: {
                      color: '#10B981',
                      fontSize: '0.875rem',
                      marginTop: '0.25rem',
                      fontFamily: 'Inter, system-ui, sans-serif',
                      fontWeight: '500',
                    },
                    spinner: {
                      color: '#2563EB',
                      width: '1.5rem',
                      height: '1.5rem',
                    },
                    formFieldAction: {
                      color: '#2563EB',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      fontFamily: 'Inter, system-ui, sans-serif',
                      textDecoration: 'none',
                      transition: 'color 0.2s ease-out',
                      '&:hover': {
                        color: '#1E40AF',
                        textDecoration: 'underline',
                        textUnderlineOffset: '2px',
                      },
                      '&:focus': {
                        outline: 'none',
                        boxShadow: '0 0 0 2px rgba(37, 99, 235, 0.2)',
                        borderRadius: '0.25rem',
                      },
                    },
                  }
                }}
              />
            </div>

            {/* Footer Links */}
            <div className="text-center animate-slide-up" style={{ animationDelay: '500ms' }}>
              <div className="flex items-center justify-center space-x-6 text-sm text-jobblogg-text-muted">
                <Link
                  to="/privacy-policy"
                  className="hover:text-jobblogg-primary transition-colors duration-200"
                >
                  Personvern
                </Link>
                <span className="text-jobblogg-border">•</span>
                <Link
                  to="/terms-of-service"
                  className="hover:text-jobblogg-primary transition-colors duration-200"
                >
                  Bruksvilkår
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignIn;
